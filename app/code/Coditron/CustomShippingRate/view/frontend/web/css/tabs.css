/* Tab Navigation Styles */
.wk-mp-tabs-container {
    margin-bottom: 20px;
    border-bottom: 1px solid #e3e3e3;
}

.wk-mp-tabs-nav {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    border-bottom: 1px solid #ddd;
}

.wk-mp-tab-item {
    margin: 0;
    padding: 0;
    border: none;
    background: none;
}

.wk-mp-tab-link {
    display: block;
    padding: 12px 24px;
    text-decoration: none;
    color: #333;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-bottom: none;
    margin-right: 2px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.wk-mp-tab-link:hover {
    background-color: #e8e8e8;
    color: #333;
    text-decoration: none;
}

.wk-mp-tab-item.active .wk-mp-tab-link {
    background-color: #fff;
    color: #007bdb;
    border-bottom: 2px solid #007bdb;
    position: relative;
    z-index: 1;
}

/* Tab Content Styles */
.wk-mp-tab-content {
    display: none;
    padding: 20px 0;
    animation: fadeIn 0.3s ease-in-out;
}

.wk-mp-tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Grid Container Styles */
#shipping-methods-grid,
#threshold-grid-container {
    margin-top: 20px;
}

/* Hide threshold grid initially */
#free-shipping-threshold #threshold-grid-container {
    display: block;
}

/* Loading Mask Styles */
.admin__data-grid-loading-mask {
    position: relative;
    background: rgba(255, 255, 255, 0.8);
    padding: 40px;
    text-align: center;
    min-height: 200px;
}

.admin__data-grid-loader {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bdb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .wk-mp-tabs-nav {
        flex-direction: column;
    }
    
    .wk-mp-tab-link {
        margin-right: 0;
        margin-bottom: 2px;
        border-radius: 4px;
    }
    
    .wk-mp-tab-item.active .wk-mp-tab-link {
        border-bottom: 1px solid #ddd;
        border-left: 4px solid #007bdb;
    }
}

/* Integration with existing Webkul styles */
.wk-mpsellercategory-container .wk-mp-tabs-container {
    background: transparent;
}

.wk-mpsellercategory-container .wk-mp-tab-content .page-main-actions {
    margin-bottom: 20px;
}

/* Error message styling */
.message.message-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 4px;
    margin: 20px 0;
}
