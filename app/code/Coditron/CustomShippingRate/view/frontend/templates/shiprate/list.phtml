<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<div class="wk-mpsellercategory-container">
    <!-- Tab Navigation -->
    <div class="wk-mp-tabs-container">
        <ul class="wk-mp-tabs-nav">
            <li class="wk-mp-tab-item active" data-tab="shipping-methods">
                <a href="#shipping-methods" class="wk-mp-tab-link">
                    <?= $escaper->escapeHtml(__('Shipping Methods')); ?>
                </a>
            </li>
            <li class="wk-mp-tab-item" data-tab="free-shipping-threshold">
                <a href="#free-shipping-threshold" class="wk-mp-tab-link">
                    <?= $escaper->escapeHtml(__('Free Shipping Threshold')); ?>
                </a>
            </li>
        </ul>
    </div>

    <!-- Shipping Methods Tab Content -->
    <div id="shipping-methods" class="wk-mp-tab-content active">
        <div class="page-main-actions">
            <div class="page-actions-placeholder"></div>
            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
                    <div class="page-actions-buttons">
                        <button id="add" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                        onclick="location.href
                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
                        data-ui-id="add-button">
                            <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div id="shipping-methods-grid">
            <?= /* @noEscape */ $block->getChildHtml(); ?>
        </div>
    </div>

    <!-- Free Shipping Threshold Tab Content -->
    <div id="free-shipping-threshold" class="wk-mp-tab-content">
        <div class="page-main-actions">
            <div class="page-actions-placeholder"></div>
            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
                    <div class="page-actions-buttons">
                        <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                        onclick="location.href
                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/thresholdnew'))?>';"
                        data-ui-id="add-threshold-button">
                            <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div id="threshold-grid-container">
            <p>Free Shipping Threshold functionality will be loaded here.</p>
            <p>Click "Add New Threshold" to create a threshold.</p>
        </div>
    </div>
</div>

<style>
.wk-mp-tabs-nav {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    border-bottom: 1px solid #ddd;
}
.wk-mp-tab-item {
    margin: 0;
    padding: 0;
}
.wk-mp-tab-link {
    display: block;
    padding: 12px 24px;
    text-decoration: none;
    color: #333;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-bottom: none;
    margin-right: 2px;
}
.wk-mp-tab-item.active .wk-mp-tab-link {
    background-color: #fff;
    color: #007bdb;
    border-bottom: 2px solid #007bdb;
}
.wk-mp-tab-content {
    display: none;
    padding: 20px 0;
}
.wk-mp-tab-content.active {
    display: block;
}
</style>

<script type="text/javascript">
require(['jquery'], function($) {
    $(document).ready(function() {
        // Tab switching functionality
        $('.wk-mp-tab-link').on('click', function(e) {
            e.preventDefault();

            var targetTab = $(this).attr('href').substring(1);

            // Remove active class from all tabs and content
            $('.wk-mp-tab-item').removeClass('active');
            $('.wk-mp-tab-content').removeClass('active');

            // Add active class to clicked tab and corresponding content
            $(this).parent().addClass('active');
            $('#' + targetTab).addClass('active');

        });
    });
});
</script>
