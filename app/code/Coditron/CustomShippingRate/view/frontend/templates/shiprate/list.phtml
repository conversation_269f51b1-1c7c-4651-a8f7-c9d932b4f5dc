<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<div class="wk-mpsellercategory-container">
    <!-- Tab Navigation -->
    <div class="wk-mp-tabs-container">
        <ul class="wk-mp-tabs-nav">
            <li class="wk-mp-tab-item active" data-tab="shipping-methods">
                <a href="#shipping-methods" class="wk-mp-tab-link">
                    <?= $escaper->escapeHtml(__('Shipping Methods')); ?>
                </a>
            </li>
            <li class="wk-mp-tab-item" data-tab="free-shipping-threshold">
                <a href="#free-shipping-threshold" class="wk-mp-tab-link">
                    <?= $escaper->escapeHtml(__('Free Shipping Threshold')); ?>
                </a>
            </li>
        </ul>
    </div>

    <!-- Shipping Methods Tab Content -->
    <div id="shipping-methods" class="wk-mp-tab-content active">
        <div class="page-main-actions">
            <div class="page-actions-placeholder"></div>
            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
                    <div class="page-actions-buttons">
                        <button id="add" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                        onclick="location.href
                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
                        data-ui-id="add-button">
                            <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div id="shipping-methods-grid">
            <div id="sellership_rates_list_front"></div>
        </div>
    </div>

    <!-- Free Shipping Threshold Tab Content -->
    <div id="free-shipping-threshold" class="wk-mp-tab-content">
        <div class="page-main-actions">
            <div class="page-actions-placeholder"></div>
            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
                    <div class="page-actions-buttons">
                        <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                        onclick="location.href
                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/thresholdnew'))?>';"
                        data-ui-id="add-threshold-button">
                            <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div id="threshold-grid-container">
            <div id="sellership_threshold_list_front"></div>
        </div>
    </div>
</div>

<script type="text/javascript">
require(['jquery'], function($) {
    $(document).ready(function() {
        // Tab switching functionality
        $('.wk-mp-tab-link').on('click', function(e) {
            e.preventDefault();

            var targetTab = $(this).attr('href').substring(1);

            // Remove active class from all tabs and content
            $('.wk-mp-tab-item').removeClass('active');
            $('.wk-mp-tab-content').removeClass('active');

            // Add active class to clicked tab and corresponding content
            $(this).parent().addClass('active');
            $('#' + targetTab).addClass('active');
        });
    });
});
</script>
