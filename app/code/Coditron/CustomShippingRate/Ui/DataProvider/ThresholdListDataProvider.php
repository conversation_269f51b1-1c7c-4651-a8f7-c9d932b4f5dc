<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Ui\DataProvider;

use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Magento\Framework\Api\Filter;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

/**
 * Class ThresholdListDataProvider
 * Data provider for free shipping threshold listing
 */
class ThresholdListDataProvider extends AbstractDataProvider
{
    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @var array
     */
    protected $loadedData;

    /**
     * Constructor
     *
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param MarketplaceHelper $marketplaceHelper
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        MarketplaceHelper $marketplaceHelper,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->marketplaceHelper = $marketplaceHelper;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }

        // Filter collection to show only threshold records (where min_amount is not null)
        // and only for current seller
        $sellerId = $this->marketplaceHelper->getCustomerId();
        
        $this->collection->addFieldToFilter('seller_id', $sellerId);
        $this->collection->addFieldToFilter('min_amount', ['notnull' => true]);
        $this->collection->addFieldToFilter('min_amount', ['gt' => 0]);

        $items = $this->collection->toArray();
        
        // Process countries field for display
        foreach ($items['items'] as &$item) {
            if (isset($item['countries'])) {
                $countries = explode(',', $item['countries']);
                $item['countries_display'] = $this->getCountryNames($countries);
            }
        }

        $this->loadedData = $items;
        
        return $this->loadedData;
    }

    /**
     * Add filter
     *
     * @param Filter $filter
     * @return void
     */
    public function addFilter(Filter $filter)
    {
        if ($filter->getField() !== 'fulltext') {
            $this->collection->addFieldToFilter(
                $filter->getField(),
                [$filter->getConditionType() => $filter->getValue()]
            );
        }
    }

    /**
     * Get country names from codes
     *
     * @param array $countryCodes
     * @return string
     */
    private function getCountryNames(array $countryCodes): string
    {
        $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
        $countryFactory = $objectManager->create(\Magento\Directory\Model\CountryFactory::class);
        
        $countryNames = [];
        foreach ($countryCodes as $code) {
            $country = $countryFactory->create()->loadByCode(trim($code));
            if ($country->getId()) {
                $countryNames[] = $country->getName();
            }
        }
        
        return implode(', ', $countryNames);
    }
}
