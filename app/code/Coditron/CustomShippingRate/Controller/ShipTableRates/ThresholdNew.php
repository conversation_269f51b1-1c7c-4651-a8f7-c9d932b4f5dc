<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\View\Result\Page;

/**
 * Class ThresholdNew
 * Controller for creating new free shipping thresholds
 */
class ThresholdNew extends \Webkul\MpSellerCategory\Controller\AbstractCategory implements HttpGetActionInterface
{
    /**
     * Execute Method
     *
     * @return \Magento\Framework\View\Result\Page|\Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        /** @var Page $resultPage */
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('mpsellership_layout2_threshold_edit');
        }

        $resultPage->getConfig()->getTitle()->set(__('New Free Shipping Threshold'));
        
        return $resultPage;
    }
}
