<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\View\Result\Page;

/**
 * Class ThresholdManage
 * Controller for managing free shipping thresholds
 */
class ThresholdManage extends \Webkul\MpSellerCategory\Controller\AbstractCategory implements HttpGetActionInterface
{
    /**
     * Execute Method
     *
     * @return \Magento\Framework\View\Result\Page|\Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        /** @var Page $resultPage */
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('sellership_layout2_threshold_manage');
        }

        $resultPage->getConfig()->getTitle()->set(__('Manage Free Shipping Thresholds'));
        
        return $resultPage;
    }
}
